#!/usr/bin/env python3
"""
Solution script for the mouse-trail CTF challenge.
This script plots the mouse coordinates to reveal the hidden flag.
"""

import matplotlib.pyplot as plt

def solve_mouse_trail():
    """Read mouse movements and plot them to reveal the flag"""
    x_vals = []
    y_vals = []
    
    print("Reading mouse movement data...")
    
    try:
        with open("mouse_movements.txt", "r") as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                try:
                    x, y = map(int, line.split(","))
                    x_vals.append(x)
                    y_vals.append(y)
                except ValueError as e:
                    print(f"Warning: Could not parse line {line_num}: {line}")
                    continue
    except FileNotFoundError:
        print("Error: mouse_movements.txt not found!")
        print("Make sure you have the challenge file in the current directory.")
        return
    
    if not x_vals:
        print("Error: No valid coordinates found in the file!")
        return
    
    print(f"Loaded {len(x_vals)} coordinate pairs")
    
    # Create the plot
    plt.figure(figsize=(20, 6))
    plt.scatter(x_vals, y_vals, s=1, color="blue", alpha=0.7)

    # Invert Y axis for natural screen coordinates (origin at top-left)
    plt.gca().invert_yaxis()

    # Remove axes for cleaner look
    plt.axis('off')

    # Set title
    plt.title("Mouse Movement Trail - CTF Challenge", fontsize=16, pad=20)
    
    # Adjust layout to fit the content
    plt.tight_layout()
    
    print("Displaying the plot...")
    print("Look carefully at the pattern formed by the dots!")
    
    # Show the plot
    plt.show()
    
    # Also save the plot as an image
    plt.savefig("mouse_trail_solution.png", dpi=300, bbox_inches='tight')
    print("Plot saved as 'mouse_trail_solution.png'")

def main():
    """Main function"""
    print("=" * 50)
    print("Mouse Trail CTF Challenge - Solution")
    print("=" * 50)
    
    solve_mouse_trail()
    
    print("\nIf you can see the flag in the plot, you've solved the challenge!")
    print("The flag should be clearly visible as text formed by the coordinate points.")

if __name__ == "__main__":
    main()
