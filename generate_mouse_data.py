#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to generate mouse movement data that spells out a flag when plotted.
This creates the challenge file mouse_movements.txt
"""

import random
import math

def draw_letter(letter, start_x, start_y, scale=50):
    """Generate coordinates for drawing a letter"""
    coords = []
    
    if letter == 'F':
        # Draw F
        coords.extend([(start_x, start_y + i) for i in range(0, scale)])  # Left vertical line
        coords.extend([(start_x + i, start_y) for i in range(0, scale//2)])  # Top horizontal
        coords.extend([(start_x + i, start_y + scale//2) for i in range(0, scale//3)])  # Middle horizontal
    
    elif letter == 'L':
        # Draw L
        coords.extend([(start_x, start_y + i) for i in range(0, scale)])  # Left vertical line
        coords.extend([(start_x + i, start_y + scale - 1) for i in range(0, scale//2)])  # Bottom horizontal
    
    elif letter == 'A':
        # Draw A
        coords.extend([(start_x, start_y + i) for i in range(scale//3, scale)])  # Left vertical
        coords.extend([(start_x + scale//2, start_y + i) for i in range(scale//3, scale)])  # Right vertical
        coords.extend([(start_x + i, start_y) for i in range(0, scale//2)])  # Top horizontal
        coords.extend([(start_x + i, start_y + scale//2) for i in range(0, scale//2)])  # Middle horizontal
    
    elif letter == 'G':
        # Draw G (simplified)
        coords.extend([(start_x + i, start_y) for i in range(0, scale//2)])  # Top
        coords.extend([(start_x, start_y + i) for i in range(0, scale)])  # Left side
        coords.extend([(start_x + i, start_y + scale - 1) for i in range(0, scale//2)])  # Bottom
        coords.extend([(start_x + scale//2 - 1, start_y + i) for i in range(scale//2, scale)])  # Right side bottom
        coords.extend([(start_x + i, start_y + scale//2) for i in range(scale//4, scale//2)])  # Middle horizontal
    
    elif letter == '{':
        # Draw opening brace
        coords.extend([(start_x + scale//4, start_y + i) for i in range(0, scale//4)])
        coords.extend([(start_x + i, start_y + scale//4) for i in range(0, scale//4)])
        coords.extend([(start_x, start_y + i) for i in range(scale//4, 3*scale//4)])
        coords.extend([(start_x + i, start_y + 3*scale//4) for i in range(0, scale//4)])
        coords.extend([(start_x + scale//4, start_y + i) for i in range(3*scale//4, scale)])
    
    elif letter == '}':
        # Draw closing brace
        coords.extend([(start_x, start_y + i) for i in range(0, scale//4)])
        coords.extend([(start_x + i, start_y + scale//4) for i in range(0, scale//4)])
        coords.extend([(start_x + scale//4, start_y + i) for i in range(scale//4, 3*scale//4)])
        coords.extend([(start_x + i, start_y + 3*scale//4) for i in range(0, scale//4)])
        coords.extend([(start_x, start_y + i) for i in range(3*scale//4, scale)])
    
    elif letter == 'M':
        # Draw M
        coords.extend([(start_x, start_y + i) for i in range(0, scale)])  # Left vertical
        coords.extend([(start_x + scale//2, start_y + i) for i in range(0, scale)])  # Right vertical
        coords.extend([(start_x + i, start_y + i) for i in range(0, scale//2)])  # Left diagonal
        coords.extend([(start_x + scale//2 - i, start_y + i) for i in range(0, scale//2)])  # Right diagonal
    
    elif letter == 'O':
        # Draw O (circle approximation)
        center_x = start_x + scale//4
        center_y = start_y + scale//2
        radius = scale//4
        for angle in range(0, 360, 5):
            x = center_x + int(radius * math.cos(math.radians(angle)))
            y = center_y + int(radius * math.sin(math.radians(angle)))
            coords.append((x, y))
    
    elif letter == 'U':
        # Draw U
        coords.extend([(start_x, start_y + i) for i in range(0, 3*scale//4)])  # Left vertical
        coords.extend([(start_x + scale//2, start_y + i) for i in range(0, 3*scale//4)])  # Right vertical
        coords.extend([(start_x + i, start_y + 3*scale//4) for i in range(0, scale//2)])  # Bottom horizontal
    
    elif letter == 'S':
        # Draw S
        coords.extend([(start_x + i, start_y) for i in range(0, scale//2)])  # Top
        coords.extend([(start_x, start_y + i) for i in range(0, scale//2)])  # Left top
        coords.extend([(start_x + i, start_y + scale//2) for i in range(0, scale//2)])  # Middle
        coords.extend([(start_x + scale//2 - 1, start_y + i) for i in range(scale//2, scale)])  # Right bottom
        coords.extend([(start_x + i, start_y + scale - 1) for i in range(0, scale//2)])  # Bottom
    
    elif letter == 'E':
        # Draw E
        coords.extend([(start_x, start_y + i) for i in range(0, scale)])  # Left vertical
        coords.extend([(start_x + i, start_y) for i in range(0, scale//2)])  # Top horizontal
        coords.extend([(start_x + i, start_y + scale//2) for i in range(0, scale//3)])  # Middle horizontal
        coords.extend([(start_x + i, start_y + scale - 1) for i in range(0, scale//2)])  # Bottom horizontal
    
    elif letter == '_':
        # Draw underscore
        coords.extend([(start_x + i, start_y + scale - 1) for i in range(0, scale//2)])
    
    elif letter == 'T':
        # Draw T
        coords.extend([(start_x + i, start_y) for i in range(0, scale//2)])  # Top horizontal
        coords.extend([(start_x + scale//4, start_y + i) for i in range(0, scale)])  # Vertical
    
    elif letter == 'R':
        # Draw R
        coords.extend([(start_x, start_y + i) for i in range(0, scale)])  # Left vertical
        coords.extend([(start_x + i, start_y) for i in range(0, scale//2)])  # Top horizontal
        coords.extend([(start_x + scale//2 - 1, start_y + i) for i in range(0, scale//2)])  # Right top vertical
        coords.extend([(start_x + i, start_y + scale//2) for i in range(0, scale//2)])  # Middle horizontal
        coords.extend([(start_x + i, start_y + scale//2 + i) for i in range(0, scale//2)])  # Diagonal
    
    elif letter == 'I':
        # Draw I
        coords.extend([(start_x + i, start_y) for i in range(0, scale//2)])  # Top horizontal
        coords.extend([(start_x + scale//4, start_y + i) for i in range(0, scale)])  # Vertical
        coords.extend([(start_x + i, start_y + scale - 1) for i in range(0, scale//2)])  # Bottom horizontal
    
    elif letter == 'L':
        # Draw L (already defined above, but keeping for clarity)
        coords.extend([(start_x, start_y + i) for i in range(0, scale)])  # Left vertical line
        coords.extend([(start_x + i, start_y + scale - 1) for i in range(0, scale//2)])  # Bottom horizontal
    
    return coords

def add_noise_coordinates(coords, noise_factor=0.1):
    """Add some noise coordinates to make it look more realistic"""
    noisy_coords = []
    total_coords = len(coords)
    noise_count = int(total_coords * noise_factor)
    
    # Add original coordinates
    noisy_coords.extend(coords)
    
    # Add random noise coordinates
    if coords:
        min_x = min(coord[0] for coord in coords)
        max_x = max(coord[0] for coord in coords)
        min_y = min(coord[1] for coord in coords)
        max_y = max(coord[1] for coord in coords)
        
        for _ in range(noise_count):
            noise_x = random.randint(min_x - 100, max_x + 100)
            noise_y = random.randint(min_y - 100, max_y + 100)
            noisy_coords.append((noise_x, noise_y))
    
    # Shuffle to mix noise with real coordinates
    random.shuffle(noisy_coords)
    return noisy_coords

def generate_flag_coordinates():
    """Generate coordinates that spell out FLAG{MOUSE_TRAIL}"""
    all_coords = []
    
    # Define the flag text
    flag_text = "FLAG{MOUSE_TRAIL}"
    
    # Starting position
    start_x = 100
    start_y = 100
    letter_spacing = 60
    
    current_x = start_x
    
    for letter in flag_text:
        if letter == ' ':
            current_x += letter_spacing // 2
            continue
            
        letter_coords = draw_letter(letter, current_x, start_y)
        all_coords.extend(letter_coords)
        current_x += letter_spacing
    
    # Add some noise to make it more challenging
    all_coords = add_noise_coordinates(all_coords, noise_factor=0.15)
    
    return all_coords

def main():
    """Generate the mouse_movements.txt file"""
    print("Generating mouse movement data...")
    
    # Set random seed for reproducible results
    random.seed(42)
    
    # Generate coordinates
    coordinates = generate_flag_coordinates()
    
    # Write to file
    with open("mouse_movements.txt", "w") as f:
        for x, y in coordinates:
            f.write(f"{x},{y}\n")
    
    print(f"Generated {len(coordinates)} coordinate pairs in mouse_movements.txt")
    print("Challenge file created successfully!")

if __name__ == "__main__":
    main()
