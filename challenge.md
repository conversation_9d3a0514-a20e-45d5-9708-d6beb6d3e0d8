# Mouse Trail

**Category:** misc  
**Points:** 100  
**Author:** 2027aliu  

## Description

I was tracking my mouse movements while working on some secret documents. The data got corrupted and now I have thousands of coordinate pairs logged. Can you help me figure out what I was drawing?

## Files
- `mouse_movements.txt` - The mouse movement data

## Hints
- Try plotting the coordinates
- The Y-axis might need to be inverted for proper viewing
- Look for patterns in the plotted points

## Flag Format
`FLAG{...}`

## Solution Approach
1. Parse the coordinate pairs from the text file
2. Plot them using a visualization library like matplotlib
3. Invert the Y-axis to match screen coordinates
4. The flag will be visible as text formed by the plotted points

## Expected Flag
`FLAG{MOUSE_TRAIL}`
